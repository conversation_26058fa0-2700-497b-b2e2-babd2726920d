package com.taktile.client

import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import com.taktile.rep.DecisionControl
import com.taktile.rep.DecisionMetadata
import com.taktile.rep.DecisionResponse
import com.taktile.rep.ExecutionMode
import com.taktile.rep.SandboxDecisionRequest
import com.taktile.rep.SchemaRep
import io.micrometer.core.instrument.logging.LoggingMeterRegistry
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.util.UUID

class TaktileClientTest {

  @Test
  fun `test flow versions endpoint`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    try {
      val versions = client.getFlowVersions("middesk-kyb-flow")
      println("Flow versions response: $versions")
    } catch (e: Exception) {
      println("Error getting flow versions: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with actual application payload`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // Create a request that matches exactly what the application is sending
    val actualPayload = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0b747cfe-758c-4d2c-8de4-816f381d6af6",
          "business_name": "Capital application 3",
          "business_dba": null,
          "phone_number": "*************",
          "ein": "123123123",
          "incorporation_state": "AZ",
          "associated_person": "Justin McKibben",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postal_code": "11206",
            "country": "US"
          }
        },
        "metadata": {
          "version": "1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = actualPayload
      )
      println("Actual payload request response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with actual payload request: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with exact curl payload`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // This is EXACTLY what your curl command sends - notice the differences
    val exactCurlPayload = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0e7f1c64-fd11-41c1-9a64-b03559e14f33",
          "business_name": "Capital application 3",
          "business_dba": "",
          "ein": "123123123",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postalCode": "11206",
            "country": "US"
          },
          "associated_person": "Justin McKibben"
        },
        "metadata": {
          "version": "v1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = exactCurlPayload
      )
      println("Exact curl payload response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with exact curl payload: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with hybrid payload - snake case address`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // Try the curl payload but with snake_case address to see if that's the issue
    val hybridPayload = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0e7f1c64-fd11-41c1-9a64-b03559e14f33",
          "business_name": "Capital application 3",
          "business_dba": "",
          "ein": "123123123",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postal_code": "11206",
            "country": "US"
          },
          "associated_person": "Justin McKibben"
        },
        "metadata": {
          "version": "v1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = hybridPayload
      )
      println("Hybrid payload (snake_case address) response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with hybrid payload: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test decide endpoint with minimal payload like curl`() = runBlocking {
    val metrics = Metrics(LoggingMeterRegistry())
    val httpClient = TaktileHttpClient(
      environment = TaktileHttpClient.Environment.Sandbox,
      metrics = metrics
    )

    val client = TaktileClient(
      httpClient = httpClient,
      taktileApiKey = ProtectedString("30fbeb7f-7064-4032-b926-4ce951285db4"),
      environment = TaktileHttpClient.Environment.Sandbox
    )

    // Create a minimal request similar to the working curl command
    val rawJson = """
      {
        "data": {
          "business_guid": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "capital_application_guid": "0e7f1c64-fd11-41c1-9a64-b03559e14f33",
          "business_name": "Capital application 3",
          "business_dba": "",
          "ein": "123123123",
          "address": {
            "line1": "123 Melrose St",
            "line2": "",
            "city": "New York",
            "state": "NY",
            "postalCode": "11206",
            "country": "US"
          },
          "associated_person": "Justin McKibben"
        },
        "metadata": {
          "version": "v1.0",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa"
        },
        "control": {
          "execution_mode": "async"
        }
      }
    """.trimIndent()

    try {
      val response = client.request.request(
        httpMethod = io.ktor.http.HttpMethod.Post,
        path = "/run/api/v1/flows/middesk-kyb-flow/sandbox/decide",
        body = rawJson
      )
      println("Raw JSON request response: ${response.statusCode}")
      println("Response body: ${response.readResponseBody()}")
    } catch (e: Exception) {
      println("Error with raw JSON request: ${e.message}")
      e.printStackTrace()
    }
  }

  @Test
  fun `test DecisionResponse deserialization with missing raw_provider_responses field`() {
    val objectMapper = taktileObjectMapper

    // This JSON response is missing the raw_provider_responses field, which was causing the original error
    val jsonResponse = """
      {
        "status": "pending",
        "metadata": {
          "environment": "sandbox",
          "version": "v1.1",
          "entity_id": "822ce1c7-bfad-4f3e-bd13-f7da47dc88aa",
          "timestamp": "2025-07-17T19:42:24.139629+00:00",
          "decision_id": "637a3335-9dce-4b44-b5ae-32d251dae65b",
          "decision_url": "https://app.taktile.com/decide/display/decision?workspace_id=73bf4686-44b7-45b4-bc99-20946ff072de&flow_slug=middesk-kyb-flow&version_id=11675a62-d466-4959-b154-5dad11465769&decision_id=637a3335-9dce-4b44-b5ae-32d251dae65b"
        }
      }
    """.trimIndent()

    try {
      val response = objectMapper.readValue(jsonResponse, DecisionResponse::class.java)
      println("Successfully deserialized DecisionResponse: $response")

      // Verify that the missing fields are null
      assert(response.rawProviderResponses == null)
      assert(response.rawProviderRequests == null)
      assert(response.status == "pending")
      assert(response.metadata.environment == "sandbox")

    } catch (e: Exception) {
      println("Error deserializing DecisionResponse: ${e.message}")
      e.printStackTrace()
      throw e
    }
  }
}

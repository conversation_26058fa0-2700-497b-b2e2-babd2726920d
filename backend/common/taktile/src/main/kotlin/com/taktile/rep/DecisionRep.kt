package com.taktile.rep

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionRequest<T>(
  val data: T,
  val metadata: DecisionMetadata,
  val control: DecisionControl? = null,
)



@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionMetadata(
  val version: String? = null,
  @JsonProperty("entity_id") val entityId: String? = null,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionControl(
  @JsonProperty("execution_mode") val executionMode: ExecutionMode = ExecutionMode.SYNC,
)

enum class ExecutionMode(
  @JsonValue val value: String
) {
  SYNC("sync"),
  ASYNC("async")
}

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class DecisionResponse(
  val data: Map<String, Any>? = null,
  val status: String,
  val metadata: ResponseMetadata,
  @JsonProperty("raw_provider_responses")
  val rawProviderResponses: List<RawProviderResponse> = emptyList(),
  @JsonProperty("raw_provider_requests")
  val rawProviderRequests: List<RawProviderRequest> = emptyList(),
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ResponseMetadata(
  val environment: String,
  val version: String,
  @JsonProperty("entity_id") val entityId: String,
  val timestamp: String,
  @JsonProperty("decision_id") val decisionId: String,
  @JsonProperty("decision_url") val decisionUrl: String,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RawProviderResponse(
  val provider: String,
  val resource: String,
  @JsonProperty("resource_config_id") val resourceConfigId: String,
  @JsonProperty("node_id") val nodeId: String,
  @JsonProperty("node_name") val nodeName: String,
  @JsonProperty("raw_response") val rawResponse: String,
  @JsonProperty("provider_response_time") val providerResponseTime: Int?,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RawProviderRequest(
  val provider: String,
  val resource: String,
  @JsonProperty("resource_config_id") val resourceConfigId: String,
  @JsonProperty("node_id") val nodeId: String,
  @JsonProperty("node_name") val nodeName: String,
  @JsonProperty("raw_request") val rawRequest: RawRequestDetails,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RawRequestDetails(
  val headers: String,
  val body: String,
  val method: String,
  val url: String,
)

@JsonNaming(value = PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SandboxMockData(
  // Mock data for data integrations, and nested flows in sandbox mode.
  // Each key in the object should map to a node name in the graph.
  // The value should be the mock data for that node.
  val mockData: Map<String, Any> = emptyMap()
)

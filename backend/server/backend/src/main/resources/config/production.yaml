# This config is used in the REAL PRODUCTION environment.

app:
  appBaseUrl: https://app.highbeam.co
  apiBaseUrl: https://api.highbeam.co

airwallex:
  environment: Production
  apiKey:
    type: Plaintext
    value: production-airwallex-api-key
  clientId: production-airwallex-client-id
  slackWebhookPath: productionAirwallexSlackWebhookPath

auth0:
  baseUrl: https://highbeam.us.auth0.com
  clientId: p8P6Bg69AFdm7xZ2tAp3UjvXrPvDo2VC
  clientSecret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: auth0-client-secret
    versionId: latest

aws:
  credentials:
    accessKeyId: AKIATUVULIWSJTOUZWF7
    secretAccessKey:
      type: GcpSecret
      projectId: highbeam-production
      secretId: transfer-user-aws-secret-key
      versionId: latest

backendV2:
  baseUrl: https://v2.api.highbeam.co
  jwtMechanism:
    source: Static
    issuer: https://highbeam.co/
    algorithm: Hmac256
    secret:
      type: GcpSecret
      projectId: highbeam-production
      secretId: jwt-secret
      versionId: latest

bankingConfig:
  slackWebhooks:
    monitoring: triggers/T01GWSTDAN6/*************/a677a0eef03315a9f268739907523297
  promotionAprilReferralGuids:
    - 7bb969ae-5386-4bd8-b079-5fdd2e1852a8

businessMetrics:
  enabled: true
  slackWebhooks:
    stateChange: triggers/T01GWSTDAN6/*************/a4336641ace230279ff32939e26b08ca
  monthlySummarySpreadsheetId: 1ouV8cLJtXKZ5A-B2dvbmQv4ma_kGB7dQcPl25wWYlqo
  weeklySummarySpreadsheetId: 1URhpMDm7ZCdiPDcO-qu951xg4pIcTyVL4Sxi2Zq3_kA

clock:
  type: Real

credit:
  lineOfCreditOfferAcceptedSlackWebhookPath: workflows/T01GWSTDAN6/A04TS9XRH70/******************/uiHP2mLMfRJ5ls3qwVhFky74
  requestBankVerificationLetterSlackWebhookPath: triggers/T01GWSTDAN6/*************/f7aa8e2a8f5bf9dcb00433c4c1283efd
  lineOfCreditApprovalProcessedSlackWebhookPath: workflows/T01GWSTDAN6/A05AT1M9HEG/******************/fBRHaRacGHbJrS4ptqqusDs3
  lineOfCreditGenericMessageSlackWebhookPath: workflows/T01GWSTDAN6/A05CAPBGM4J/******************/plxCo5jxdmwxwVrKoNofWdzc
  lineOfCreditAppSubmittedSlackWebhookPath: workflows/T01GWSTDAN6/A058QBA0THV/******************/ACNiODP16c4uMkTHFAOkneiP
  highbeamSpvCollectionUnitAccountId: 3837698
  highbeamOldFundingUnitAccountId: 1569875
  highbeamThreadLineOfCreditCounterpartyId: 458356
  mustacheTemplateRoot: templates
  externalLenderLineOfCreditUnitAccountId:
    Ember: 3139972
    Paperstack: 3992357

chargeCard:
  chargeCardWebhookPath: services/T01GWSTDAN6/B07CPN1RPE0/Ft7FE4H52tUl0IIdO1LlYOZi
  highbeamThreadChargeCardRepaymentUnitAccountId: 1569875
  highbeamSpvCollectionUnitAccountId: 3837698

creditTransaction:
  highbeamFundingHighYieldSavingUnitAccountId: 3837661
  highbeamSpvCollectionUnitAccountId: 3837698
  highbeamOldFundingUnitAccountId: 1569875
  oldFundingAccounts:
    - 1569875
  capitalDrawdownWebhookPath: workflows/T01GWSTDAN6/A05A6BBNKG8/******************/u03mlGg6gJgkQmhyeoK7MBQy
  capitalDrawdownApprovalWebhookPath: services/T01GWSTDAN6/B07CR31F5QD/GeD6izA4bbGXVfPOYn6SHKy5
  capitalExternalLenderDrawdownWebhookPath: triggers/T01GWSTDAN6/*************/ffa3100a377b87a281a846054119a9cc
  externalLenderLineOfCreditUnitAccountId:
    Ember: 3139972
    Paperstack: 3992357
  loanTapeSubledgerUnitAccountIds:
    - 3656243

currencyCloud:
  environment: Production
  loginId: highbeam.api.user
  apiKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: currency-cloud-api-key
    versionId: latest
  webhookKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: currency-cloud-webhook-token
    versionId: latest
  slackWebhookPath: workflows/T01GWSTDAN6/A042DDPKKNW/******************/pVs4boXBTQalIqyZz12o1Y6O
  paymentFeeId: 875670ba-1534-4a5c-a6a1-181513598d8d

email:
  enabled: true
  sendgridApiKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: sendgrid-api-key
    versionId: latest
  sendgridTemplateId: d-d05050e75dd341618653d7428a8bb148

event:
  enabled:
    listening: false
    publishing: true
  project: highbeam-production

featureFlags:
  launchDarklySdkKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: launch-darkly-sdk-key
    versionId: latest

fraudMonitor:
  cardAuthorizationSlackWebhookPath: workflows/T01GWSTDAN6/A051D6MBA5D/******************/9YJHNgOQdHc6Rf5CqIzniy5i
  cardStatusSlackWebhookPath: workflows/T01GWSTDAN6/A051U37EFLG/******************/ql5ncpiMB77dbcfNs9n3wwei

googleCloudStorage:
  gcpProjectId: highbeam-production
  trustedBucketName: assets.highbeam.co
  untrustedBucketName: unscanned-assets.highbeam.co
  internalBucketName: internal.highbeam.co
  urlSigningDuration: 900 # 15 minutes

hashing:
  internalDataHashSecret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: internal-data-hash-secret
    versionId: latest

highbeamDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_JDBC_URL
  username:
    type: GcpSecret
    projectId: highbeam-production
    secretId: postgres-username
    versionId: latest
  password:
    type: GcpSecret
    projectId: highbeam-production
    secretId: postgres-password
    versionId: latest
  runMigrations: true
  connectionTimeout: 2500
  minimumIdle: 8
  maximumPoolSize: 32

hosts:
  backend: http://highbeam-backend

intercom:
  secret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: intercom-secret
    versionId: latest
  unitCoAdminUrl: https://app.unit.co

jobsDatabase:
  jdbcUrl:
    type: EnvironmentVariable
    name: HIGHBEAM_POSTGRES_JDBC_URL
  schema: quartz
  username:
    type: GcpSecret
    projectId: highbeam-production
    secretId: postgres-username
    versionId: latest
  password:
    type: GcpSecret
    projectId: highbeam-production
    secretId: postgres-password
    versionId: latest
  startDelaySeconds: 15

metrics:
  datadog:
    environment: production
    uri: https://api.us5.datadoghq.com
    apiKey:
      type: GcpSecret
      projectId: highbeam-production
      secretId: datadog-api-key
      versionId: latest
    applicationKey:
      type: GcpSecret
      projectId: highbeam-production
      secretId: datadog-application-key
      versionId: latest

name: backend

notion:
  baseUrl: https://api.notion.com/v1
  databaseId: cc45b5295dce4b3bba3b4fe3a26c3bde
  notionVersion: 2021-08-16
  secret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: notion-secret
    versionId: latest

plaid:
  environment: Production
  webhookUrl: https://api.highbeam.co/plaid/webhook
  clientId: 60b91c131c29f40010af12ec
  secret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: plaid-secret
    versionId: latest

rest:
  authentication:
    verifiers:
      - type: Jwt
        mechanisms:
          - source: Static
            issuer: https://highbeam.co/
            algorithm: Hmac256
            secret:
              type: GcpSecret
              projectId: highbeam-production
              secretId: jwt-secret
              versionId: latest
          - source: Jwk
            issuer: https://auth.highbeam.co/
            url: https://highbeam.us.auth0.com/.well-known/jwks.json
          - source: Jwk
            issuer: accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
          - source: Jwk
            issuer: https://accounts.google.com
            url: https://www.googleapis.com/oauth2/v3/certs
      - type: Token
        internalToken:
          token:
            type: GcpSecret
            projectId: highbeam-production
            secretId: internal-token
            versionId: latest
          roles: [HIGHBEAM_SERVER]
        tokens:
          - token:
              type: GcpSecret
              projectId: highbeam-production
              secretId: auth0-token
              versionId: latest
            roles: [IDENTITY_PROVIDER]
          - token:
              type: GcpSecret
              projectId: highbeam-production
              secretId: cloud-tasks-token
              versionId: latest
            roles: [CLOUD_TASKS]
  port: 8080
  parallelization:
    connectionGroupSize: 32
    workerGroupSize: 64
    callGroupSize: 256

rutter:
  clientId:
    type: GcpSecret
    projectId: highbeam-production
    secretId: rutter-client-id
    versionId: latest
  apiSecret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: rutter-api-secret
    versionId: latest
  baseUrl: https://production.rutterapi.com/versioned
  version: 2023-03-14

sentry:
  dsn: https://<EMAIL>/6175439
  environment: production

shopify:
  apiKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: shopify-api-key
    versionId: latest
  apiSecret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: shopify-api-secret
    versionId: latest
  apiScopes: read_analytics,read_all_orders,read_assigned_fulfillment_orders,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,read_discounts,read_inventory,read_locations,read_marketing_events,read_orders,read_payment_terms,read_products,read_returns,read_shipping,read_shopify_payments_disputes,read_shopify_payments_payouts,read_customers
  webhookAddress: https://api.highbeam.co/shopify-webhook
  mandatoryWebhookSlackWebhookPath: workflows/T01GWSTDAN6/5502209953317/ab91da1cf3466d166664200327bcf67a

task:
  enabled: true
  projectName: highbeam-production
  locationName: us-central1
  cloudSchedulerRequestEmail: <EMAIL>
  cloudSchedulerRequestAudience: backend-v1-tasks
  cloudTasksToken:
    type: GcpSecret
    projectId: highbeam-production
    secretId: cloud-tasks-token # corresponds to CLOUD_TASKS platform role
    versionId: latest

taktile:
  environment: Production
  apiKey:
    type: GcpSecret
    projectId: highbeam-production
    secretId: taktile-api-key
    versionId: latest
  flowSlug:
    type: Plaintext
    value: middesk-kyb-flow
  flowVersion:
    type: Plaintext
    value: "v1.1"

unitCo:
  environment: Production
  secret:
    type: GcpSecret
    projectId: highbeam-production
    secretId: unit-co-secret
    versionId: latest
  slackWebhooks:
    applicationDenied: triggers/T01GWSTDAN6/5944295063588/06928822f5fba0be66d2d2695484b34c
  webhookToken:
    type: GcpSecret
    projectId: highbeam-production
    secretId: unit-co-webhook-token
    versionId: latest

uuids:
  generation: Random

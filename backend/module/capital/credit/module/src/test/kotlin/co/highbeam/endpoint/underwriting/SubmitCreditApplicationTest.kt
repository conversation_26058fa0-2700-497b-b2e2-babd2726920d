package co.highbeam.endpoint.underwriting

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessAddress.BusinessAddressApi
import co.highbeam.api.businessDetails.BusinessDetailsApi
import co.highbeam.api.onboarding.CreditApplicationApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessAddress.BusinessAddressClient
import co.highbeam.client.businessDetails.BusinessDetailsClient
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessAddress.BusinessAddressRep
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import com.taktile.client.TaktileClient
import com.taktile.rep.DecisionRequest
import com.taktile.rep.SandboxDecisionRequest
import com.taktile.rep.SchemaRep
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZoneOffset.UTC
import java.util.UUID

internal class SubmitCreditApplicationTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @Test
  fun `happy path`() = integrationTest {
    setupTaktileMocks()
    (get<FeatureFlagService>() as FakeFeatureFlagService)[BusinessFlag.CapitalTaktileKyb] = true

    val creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    val result = creditApplicationClient.request(
      CreditApplicationApi.Submit(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationRep.Updater(),
      )
    )

    assertThat(result).isEqualTo(
      CapitalApplicationRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        state = CapitalApplicationRep.State.Submitted,
        submittedAt = clock.date(UTC),
        offer = null,
        businessDetails = null,
        userProvidedDetails = null,
      )
    )

    verifyTaktileWasCalled(creditApplication.guid)
  }

  @Test
  fun `cannot find credit application`() = integrationTest {
    assertThat(
      creditApplicationClient.request(
        CreditApplicationApi.Submit(
          businessGuid = businessGuid,
          creditApplicationGuid = UUID.randomUUID(),
          rep = CapitalApplicationRep.Updater(),
        )
      )
    ).isNull()
  }

  @Test
  fun `feature flag unset - does not send to Taktile`() = integrationTest {
    setupTaktileMocks()

    val creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    val result = creditApplicationClient.request(
      CreditApplicationApi.Submit(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationRep.Updater(),
      )
    )

    assertThat(result).isEqualTo(
      CapitalApplicationRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        state = CapitalApplicationRep.State.Submitted,
        submittedAt = clock.date(UTC),
        offer = null,
        businessDetails = null,
        userProvidedDetails = null,
      )
    )

    // Verify Taktile was NOT called
    coVerify(exactly = 0) {
      get<TaktileClient>().decide<Any>(any(), any())
    }
  }

  @Test
  fun `feature flag disabled - does not send to Taktile`() = integrationTest {
    setupTaktileMocks()

    // Disable the feature flag
    (get<FeatureFlagService>() as FakeFeatureFlagService)[BusinessFlag.CapitalTaktileKyb] = false

    val creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    val result = creditApplicationClient.request(
      CreditApplicationApi.Submit(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationRep.Updater(),
      )
    )

    assertThat(result).isEqualTo(
      CapitalApplicationRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        state = CapitalApplicationRep.State.Submitted,
        submittedAt = clock.date(UTC),
        offer = null,
        businessDetails = null,
        userProvidedDetails = null,
      )
    )

    // Verify Taktile was NOT called
    coVerify(exactly = 0) {
      get<TaktileClient>().decide<Any>(any(), any())
    }
  }

  @Test
  fun `feature flag enabled - sends to Taktile`() = integrationTest {
    setupTaktileMocks()
    (get<FeatureFlagService>() as FakeFeatureFlagService)[BusinessFlag.CapitalTaktileKyb] = true

    val creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    val result = creditApplicationClient.request(
      CreditApplicationApi.Submit(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationRep.Updater(),
      )
    )

    assertThat(result).isEqualTo(
      CapitalApplicationRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        state = CapitalApplicationRep.State.Submitted,
        submittedAt = clock.date(UTC),
        offer = null,
        businessDetails = null,
        userProvidedDetails = null,
      )
    )

    verifyTaktileWasCalled(creditApplication.guid)
  }

  private fun setupTaktileMocks() {
    coEvery {
      get<BusinessClient>().request(BusinessApi.Get(businessGuid))
    } returns BusinessRep.Complete(
      guid = businessGuid,
      name = "Test Business",
      dba = "Test DBA",
      referralLinkGuid = null,
      ownerUserGuid = UUID.randomUUID(),
      status = BusinessRep.Complete.Status.Active,
      unitCoCustomerId = "test-customer-id",
      stateOfIncorporation = null,
      naics = null,
    )

    coEvery {
      get<BusinessDetailsClient>().request(BusinessDetailsApi.GetInternal(businessGuid))
    } returns InternalBusinessDetailsRep(
      name = "Test Business",
      dba = "Test DBA",
      website = "https://test.com",
      phoneNumber = "+1234567890",
      ein = "12-3456789",
      incorporationState = "CA",
      associatedPerson = "John Doe",
    )

    coEvery {
      get<BusinessAddressClient>().request(BusinessAddressApi.Get(businessGuid))
    } returns BusinessAddressRep(
      line1 = "123 Test St",
      line2 = "Suite 100",
      city = "Test City",
      state = "CA",
      postalCode = "12345",
      country = "US",
    )

    coEvery {
      get<TaktileClient>().decide<Any>(any(), any())
    } returns mockk()
  }

  @Suppress("CyclomaticComplexMethod")
  private fun verifyTaktileWasCalled(creditApplicationGuid: UUID) {
    coVerify {
      get<TaktileClient>().decide<SchemaRep.Creator>(
        flowSlug = "middesk-kyb-flow",
        decisionRequest = match { request ->
          request is DecisionRequest<*> &&
            request.data is SchemaRep.Creator &&
            (request.data as SchemaRep.Creator).let { data ->
              data.businessGuid == businessGuid &&
                data.capitalApplicationGuid == creditApplicationGuid &&
                data.businessName == "Test Business" &&
                data.businessDba == "Test DBA" &&
                data.phoneNumber == "+1234567890" &&
                data.ein == "12-3456789" &&
                data.incorporationState == "CA" &&
                data.associatedPerson == "John Doe" &&
                data.address.line1 == "123 Test St" &&
                data.address.line2 == "Suite 100" &&
                data.address.city == "Test City" &&
                data.address.state == "CA" &&
                data.address.postalCode == "12345" &&
                data.address.country == "US"
            } &&
            request.metadata.version == "1.0" &&
            request.metadata.entityId == businessGuid.toString()
        }
      )
    }
  }
}
